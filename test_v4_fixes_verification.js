// 🔧 اختبار التحقق من إصلاحات النظام v4
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');
const path = require('path');

async function testV4Fixes() {
    console.log('🔧 اختبار التحقق من إصلاحات النظام v4...\n');
    
    try {
        // إنشاء مثيل من النظام v4
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل النظام v4');
        
        // بيانات اختبار للتحقق من الإصلاحات
        const targetUrl = 'https://testphp.vulnweb.com/artists.php';
        const timestamp = Date.now();
        const cleanUrl = targetUrl.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');
        const dateStr = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const folderName = `${cleanUrl}_${dateStr}_${timestamp}`;
        
        console.log(`🎯 الهدف: ${targetUrl}`);
        console.log(`📁 اسم المجلد الجديد: ${folderName}`);
        
        // تعيين المجلد في النظام
        if (!bugBountyCore.analysisState) {
            bugBountyCore.analysisState = {};
        }
        bugBountyCore.analysisState.currentScanFolder = folderName;
        
        // إنشاء مجلد الصور
        const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots', folderName);
        if (!fs.existsSync(screenshotsDir)) {
            fs.mkdirSync(screenshotsDir, { recursive: true });
            console.log(`📁 تم إنشاء مجلد الصور: ${folderName}`);
        }
        
        // بيانات ثغرة للاختبار
        const testVuln = {
            name: 'SQL Injection',
            type: 'sql injection',
            severity: 'High',
            url: targetUrl,
            description: 'ثغرة حقن SQL في معامل البحث',
            parameter: 'search',
            payload: "' OR 1=1 --",
            evidence: 'تم تأكيد الثغرة',
            impact: 'الوصول لقاعدة البيانات'
        };
        
        console.log('\n🔍 اختبار دالة البحث عن الصور...');
        
        // اختبار دالة البحث عن الصور مع الإصلاحات الجديدة
        const beforeImage = bugBountyCore.findRealImageForVulnerability(testVuln, 'before');
        const duringImage = bugBountyCore.findRealImageForVulnerability(testVuln, 'during');
        const afterImage = bugBountyCore.findRealImageForVulnerability(testVuln, 'after');
        
        console.log(`📸 صورة before: ${beforeImage}`);
        console.log(`📸 صورة during: ${duringImage}`);
        console.log(`📸 صورة after: ${afterImage}`);
        
        // التحقق من أسماء الصور الصحيحة
        const expectedPattern = new RegExp(`${folderName}/(before|during|after)_SQL_Injection_${cleanUrl}\\.png`);
        
        const beforeCorrect = expectedPattern.test(beforeImage);
        const duringCorrect = expectedPattern.test(duringImage);
        const afterCorrect = expectedPattern.test(afterImage);
        
        console.log(`✅ اسم صورة before صحيح: ${beforeCorrect ? 'نعم' : 'لا'}`);
        console.log(`✅ اسم صورة during صحيح: ${duringCorrect ? 'نعم' : 'لا'}`);
        console.log(`✅ اسم صورة after صحيح: ${afterCorrect ? 'نعم' : 'لا'}`);
        
        console.log('\n🔍 اختبار دالة إنشاء الصور PNG الحقيقية...');
        
        // اختبار إنشاء صور PNG حقيقية
        const beforePNG = bugBountyCore.generateRealScreenshot('SQL Injection', 'sql injection', 'before');
        const duringPNG = bugBountyCore.generateRealScreenshot('SQL Injection', 'sql injection', 'during');
        const afterPNG = bugBountyCore.generateRealScreenshot('SQL Injection', 'sql injection', 'after');
        
        // التحقق من أن الصور PNG وليس SVG
        const beforeIsPNG = beforePNG.startsWith('data:image/png;base64,');
        const duringIsPNG = duringPNG.startsWith('data:image/png;base64,');
        const afterIsPNG = afterPNG.startsWith('data:image/png;base64,');
        
        console.log(`📸 صورة before هي PNG: ${beforeIsPNG ? 'نعم' : 'لا'}`);
        console.log(`📸 صورة during هي PNG: ${duringIsPNG ? 'نعم' : 'لا'}`);
        console.log(`📸 صورة after هي PNG: ${afterIsPNG ? 'نعم' : 'لا'}`);
        
        // حفظ الصور للاختبار
        if (beforeIsPNG) {
            const base64Data = beforePNG.split(',')[1];
            const beforePath = path.join(screenshotsDir, `before_SQL_Injection_${cleanUrl}.png`);
            fs.writeFileSync(beforePath, Buffer.from(base64Data, 'base64'));
            const stats = fs.statSync(beforePath);
            console.log(`💾 تم حفظ صورة before: ${(stats.size / 1024).toFixed(2)} KB`);
        }
        
        if (duringIsPNG) {
            const base64Data = duringPNG.split(',')[1];
            const duringPath = path.join(screenshotsDir, `during_SQL_Injection_${cleanUrl}.png`);
            fs.writeFileSync(duringPath, Buffer.from(base64Data, 'base64'));
            const stats = fs.statSync(duringPath);
            console.log(`💾 تم حفظ صورة during: ${(stats.size / 1024).toFixed(2)} KB`);
        }
        
        if (afterIsPNG) {
            const base64Data = afterPNG.split(',')[1];
            const afterPath = path.join(screenshotsDir, `after_SQL_Injection_${cleanUrl}.png`);
            fs.writeFileSync(afterPath, Buffer.from(base64Data, 'base64'));
            const stats = fs.statSync(afterPath);
            console.log(`💾 تم حفظ صورة after: ${(stats.size / 1024).toFixed(2)} KB`);
        }
        
        console.log('\n🔍 اختبار دالة getLatestScreenshotFolder...');
        
        // اختبار دالة الحصول على أحدث مجلد
        const latestFolder = bugBountyCore.getLatestScreenshotFolder();
        console.log(`📁 أحدث مجلد: ${latestFolder}`);
        console.log(`✅ يستخدم المجلد الحالي: ${latestFolder === folderName ? 'نعم' : 'لا'}`);
        
        // إضافة بيانات الصور للثغرة
        testVuln.screenshots = {
            before: `./assets/modules/bugbounty/screenshots/${folderName}/before_SQL_Injection_${cleanUrl}.png`,
            during: `./assets/modules/bugbounty/screenshots/${folderName}/during_SQL_Injection_${cleanUrl}.png`,
            after: `./assets/modules/bugbounty/screenshots/${folderName}/after_SQL_Injection_${cleanUrl}.png`,
            folder: folderName
        };
        
        console.log('\n🔍 اختبار إنشاء تقرير منفصل مع الإصلاحات...');
        
        // إنشاء تقرير منفصل للاختبار
        const scanInfo = {
            scan_id: `test_fixes_${timestamp}`,
            target_url: targetUrl,
            timestamp: new Date().toISOString(),
            folder_name: folderName
        };
        
        const separateReport = await bugBountyCore.generateSeparateReport(testVuln, scanInfo);
        
        // حفظ التقرير
        const reportFileName = `Test_Fixes_Report_${timestamp}.html`;
        fs.writeFileSync(reportFileName, separateReport, 'utf8');
        const reportStats = fs.statSync(reportFileName);
        
        console.log(`📄 تم إنشاء تقرير الاختبار: ${reportFileName}`);
        console.log(`📊 حجم التقرير: ${(reportStats.size / 1024).toFixed(2)} KB`);
        
        // فحص التقرير للتأكد من الإصلاحات
        const reportContent = fs.readFileSync(reportFileName, 'utf8');
        
        const hasFolderName = reportContent.includes(folderName);
        const hasCorrectImagePaths = reportContent.includes(`before_SQL_Injection_${cleanUrl}.png`);
        const hasNoErrors = !reportContent.includes('خطأ في الوصول للمجلد') && !reportContent.includes('فشل في تحميل الصورة');
        const imageCount = (reportContent.match(/\.png/g) || []).length;
        
        console.log(`✅ التقرير يحتوي على اسم المجلد الصحيح: ${hasFolderName ? 'نعم' : 'لا'}`);
        console.log(`✅ التقرير يحتوي على مسارات الصور الصحيحة: ${hasCorrectImagePaths ? 'نعم' : 'لا'}`);
        console.log(`✅ التقرير لا يحتوي على أخطاء: ${hasNoErrors ? 'نعم' : 'لا'}`);
        console.log(`📸 عدد مراجع الصور في التقرير: ${imageCount}`);
        
        // النتيجة النهائية
        const allFixesWorking = beforeCorrect && duringCorrect && afterCorrect && 
                               beforeIsPNG && duringIsPNG && afterIsPNG &&
                               latestFolder === folderName &&
                               hasFolderName && hasCorrectImagePaths && hasNoErrors &&
                               imageCount > 0;
        
        console.log('\n🎯 نتائج اختبار الإصلاحات:');
        console.log(`✅ أسماء المجلدات صحيحة: ${beforeCorrect && duringCorrect && afterCorrect ? 'نعم' : 'لا'}`);
        console.log(`✅ الصور PNG حقيقية: ${beforeIsPNG && duringIsPNG && afterIsPNG ? 'نعم' : 'لا'}`);
        console.log(`✅ دالة المجلد تعمل: ${latestFolder === folderName ? 'نعم' : 'لا'}`);
        console.log(`✅ التقرير يعرض الصور: ${hasFolderName && hasCorrectImagePaths && hasNoErrors ? 'نعم' : 'لا'}`);
        console.log(`🎉 جميع الإصلاحات تعمل: ${allFixesWorking ? 'نعم' : 'لا'}`);
        
        if (allFixesWorking) {
            console.log('\n🎉 ممتاز! جميع الإصلاحات تعمل بشكل صحيح!');
            console.log('✅ أسماء المجلدات تستخدم اسم الرابط');
            console.log('✅ أسماء الصور تتبع النمط الصحيح');
            console.log('✅ الصور PNG حقيقية وليس SVG ملونة');
            console.log('✅ التقارير تعرض الصور بدون أخطاء');
            console.log('✅ النظام v4 جاهز للاستخدام مع الإصلاحات!');
        } else {
            console.log('\n⚠️ هناك مشاكل تحتاج إصلاح إضافي');
        }
        
        console.log(`\n📄 تقرير الاختبار: ${path.resolve(reportFileName)}`);
        console.log(`📁 مجلد الصور: ${screenshotsDir}`);
        
        return {
            success: true,
            allFixesWorking: allFixesWorking,
            folderName: folderName,
            reportFile: reportFileName,
            imageCount: imageCount,
            fixes: {
                correctNaming: beforeCorrect && duringCorrect && afterCorrect,
                pngImages: beforeIsPNG && duringIsPNG && afterIsPNG,
                folderFunction: latestFolder === folderName,
                reportDisplay: hasFolderName && hasCorrectImagePaths && hasNoErrors
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الإصلاحات:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل اختبار الإصلاحات
testV4Fixes().then(result => {
    console.log('\n🏁 انتهى اختبار الإصلاحات');
    console.log('📊 النتيجة:', result);
}).catch(error => {
    console.error('❌ فشل اختبار الإصلاحات:', error);
});
