// اختبار سريع للتحقق من الإصلاحات في النظام الأصلي
const fs = require('fs');
const path = require('path');

console.log('🔧 اختبار الإصلاحات في النظام الأصلي...');

// محاكاة البيئة
global.window = { 
    location: { href: 'https://testphp.vulnweb.com' }
};
global.document = {
    createElement: () => ({
        getContext: () => ({}),
        toDataURL: () => 'data:image/png;base64,test'
    }),
    body: { appendChild: () => {} },
    getElementById: () => null
};
global.console = console;

// تحميل النظام
let BugBountyCore;
try {
    const coreFilePath = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'BugBountyCore.js');
    const coreCode = fs.readFileSync(coreFilePath, 'utf8');
    eval(coreCode);
    BugBountyCore = global.BugBountyCore;
    console.log('✅ تم تحميل BugBountyCore');
} catch (error) {
    console.log('❌ فشل في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// اختبار الدوال الجديدة
async function testNewFunctions() {
    try {
        const bugBountyCore = new BugBountyCore();
        
        // بيانات ثغرة تجريبية
        const testVuln = {
            name: 'SQL Injection',
            url: 'https://testphp.vulnweb.com',
            target_url: 'https://testphp.vulnweb.com',
            location: 'https://testphp.vulnweb.com/login.php'
        };

        console.log('\n🧪 اختبار الدوال الجديدة:');
        
        // اختبار getCorrectFolderName
        const folderName = bugBountyCore.getCorrectFolderName(testVuln);
        console.log(`📁 اسم المجلد: ${folderName}`);
        
        // اختبار getCleanVulnerabilityName
        const cleanVulnName = bugBountyCore.getCleanVulnerabilityName(testVuln);
        console.log(`🔧 اسم الثغرة المنظف: ${cleanVulnName}`);
        
        // اختبار getCorrectImageName
        const beforeImageName = bugBountyCore.getCorrectImageName(testVuln, 'before');
        const duringImageName = bugBountyCore.getCorrectImageName(testVuln, 'during');
        const afterImageName = bugBountyCore.getCorrectImageName(testVuln, 'after');
        
        console.log(`📸 اسم صورة Before: ${beforeImageName}`);
        console.log(`📸 اسم صورة During: ${duringImageName}`);
        console.log(`📸 اسم صورة After: ${afterImageName}`);

        // التحقق من النمط الصحيح
        const expectedPattern = /^(before|during|after)_[A-Za-z0-9_]+_testphp_vulnweb_com\.png$/;
        
        const beforeCorrect = expectedPattern.test(beforeImageName);
        const duringCorrect = expectedPattern.test(duringImageName);
        const afterCorrect = expectedPattern.test(afterImageName);
        
        console.log(`\n✅ فحص النمط:`);
        console.log(`📸 Before صحيح: ${beforeCorrect ? '✅' : '❌'}`);
        console.log(`📸 During صحيح: ${duringCorrect ? '✅' : '❌'}`);
        console.log(`📸 After صحيح: ${afterCorrect ? '✅' : '❌'}`);

        // اختبار generateVisualChangesSection
        console.log(`\n📄 اختبار generateVisualChangesSection...`);
        
        // تهيئة analysisState
        bugBountyCore.analysisState = {
            currentScanFolder: 'testphp_vulnweb_com',
            vulnerabilities: [testVuln]
        };

        // إضافة صور وهمية للثغرة
        testVuln.visual_proof = {
            before_screenshot: 'data:image/png;base64,test_before',
            during_screenshot: 'data:image/png;base64,test_during',
            after_screenshot: 'data:image/png;base64,test_after'
        };

        const visualChangesHTML = bugBountyCore.generateVisualChangesSection(testVuln);
        
        // فحص المحتوى
        const containsCorrectFolder = visualChangesHTML.includes('testphp_vulnweb_com');
        const containsCorrectImageNames = visualChangesHTML.includes('before_SQL_Injection_testphp_vulnweb_com') &&
                                         visualChangesHTML.includes('during_SQL_Injection_testphp_vulnweb_com') &&
                                         visualChangesHTML.includes('after_SQL_Injection_testphp_vulnweb_com');

        console.log(`📁 يحتوي على اسم المجلد الصحيح: ${containsCorrectFolder ? '✅' : '❌'}`);
        console.log(`📸 يحتوي على أسماء الصور الصحيحة: ${containsCorrectImageNames ? '✅' : '❌'}`);

        // حفظ نموذج من HTML للفحص
        const sampleHTML = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار الإصلاحات</title>
</head>
<body>
    <h1>🧪 اختبار الإصلاحات في النظام v4</h1>
    
    <h2>📊 نتائج الاختبار:</h2>
    <p><strong>📁 اسم المجلد:</strong> ${folderName}</p>
    <p><strong>🔧 اسم الثغرة المنظف:</strong> ${cleanVulnName}</p>
    
    <h3>📸 أسماء الصور:</h3>
    <ul>
        <li>Before: ${beforeImageName}</li>
        <li>During: ${duringImageName}</li>
        <li>After: ${afterImageName}</li>
    </ul>
    
    <h3>✅ فحص النمط:</h3>
    <ul>
        <li>Before صحيح: ${beforeCorrect ? '✅' : '❌'}</li>
        <li>During صحيح: ${duringCorrect ? '✅' : '❌'}</li>
        <li>After صحيح: ${afterCorrect ? '✅' : '❌'}</li>
    </ul>
    
    <h3>📄 فحص HTML:</h3>
    <ul>
        <li>يحتوي على اسم المجلد الصحيح: ${containsCorrectFolder ? '✅' : '❌'}</li>
        <li>يحتوي على أسماء الصور الصحيحة: ${containsCorrectImageNames ? '✅' : '❌'}</li>
    </ul>
    
    <h3>📋 قسم التغيرات المرئية:</h3>
    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
        ${visualChangesHTML}
    </div>
</body>
</html>
        `;

        const testReportName = `Test_Fixes_Verification_${Date.now()}.html`;
        fs.writeFileSync(testReportName, sampleHTML);
        console.log(`💾 تم حفظ تقرير الاختبار: ${testReportName}`);

        // النتائج النهائية
        const allTestsPassed = folderName === 'testphp_vulnweb_com' &&
                              beforeCorrect && duringCorrect && afterCorrect &&
                              containsCorrectFolder && containsCorrectImageNames;

        console.log(`\n🎯 النتيجة النهائية: ${allTestsPassed ? '🎉 جميع الاختبارات نجحت!' : '⚠️ بعض الاختبارات فشلت'}`);

        if (allTestsPassed) {
            console.log('✅ الإصلاحات تعمل بشكل صحيح في النظام الأصلي');
            console.log('✅ أسماء المجلدات: testphp_vulnweb_com');
            console.log('✅ أسماء الصور: مرحلة_اسم_الثغرة_الموقع.png');
            console.log('✅ HTML يعرض المعلومات الصحيحة');
        } else {
            console.log('❌ بعض الإصلاحات تحتاج مراجعة');
        }

        return allTestsPassed;

    } catch (error) {
        console.log(`❌ خطأ في الاختبار: ${error.message}`);
        console.log(`📋 تفاصيل الخطأ: ${error.stack}`);
        return false;
    }
}

// تشغيل الاختبار
testNewFunctions()
    .then(success => {
        if (success) {
            console.log('\n🎉 اكتمل اختبار الإصلاحات بنجاح!');
            console.log('🚀 النظام v4 الأصلي محدث بالإصلاحات الجديدة');
        } else {
            console.log('\n⚠️ الاختبار كشف عن مشاكل تحتاج إصلاح');
        }
    })
    .catch(error => {
        console.log(`\n💥 خطأ في تشغيل الاختبار: ${error.message}`);
    });
