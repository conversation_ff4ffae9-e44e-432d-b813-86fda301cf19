// اختبار النظام الحقيقي مع الإصلاحات
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testSystemWithFixes() {
    console.log('🔧 اختبار النظام الحقيقي مع الإصلاحات...');
    
    try {
        // إنشاء instance من النظام
        const bugBountyCore = new BugBountyCore();
        
        // تهيئة النظام
        console.log('🚀 تهيئة النظام...');
        await bugBountyCore.initialize();
        
        // إنشاء ثغرة تجريبية
        const testVulnerability = {
            name: 'SQL Injection',
            type: 'SQL Injection',
            severity: 'High',
            url: 'https://testphp.vulnweb.com/login.php',
            location: 'https://testphp.vulnweb.com/login.php',
            target_url: 'https://testphp.vulnweb.com/login.php',
            description: 'SQL injection vulnerability in login form',
            payload: "admin' OR '1'='1' --"
        };
        
        console.log('📝 اختبار دوال الإصلاحات...');
        
        // اختبار دالة getCorrectFolderName
        const folderName = bugBountyCore.getCorrectFolderName(testVulnerability);
        console.log(`📁 اسم المجلد: ${folderName}`);
        
        // اختبار دالة getCleanVulnerabilityName
        const cleanVulnName = bugBountyCore.getCleanVulnerabilityName(testVulnerability);
        console.log(`🏷️ اسم الثغرة المنظف: ${cleanVulnName}`);
        
        // اختبار دالة getCorrectImageName
        const beforeImage = bugBountyCore.getCorrectImageName('before', cleanVulnName, folderName);
        const duringImage = bugBountyCore.getCorrectImageName('during', cleanVulnName, folderName);
        const afterImage = bugBountyCore.getCorrectImageName('after', cleanVulnName, folderName);
        
        console.log(`📸 صورة قبل: ${beforeImage}`);
        console.log(`📸 صورة أثناء: ${duringImage}`);
        console.log(`📸 صورة بعد: ${afterImage}`);
        
        // تعيين analysisState.reportId لاسم الرابط
        bugBountyCore.analysisState = {
            reportId: folderName,
            currentScanFolder: folderName,
            vulnerabilities: [testVulnerability]
        };
        
        console.log(`🆔 Report ID: ${bugBountyCore.analysisState.reportId}`);
        
        // اختبار إنشاء صورة افتراضية
        console.log('🎨 اختبار إنشاء صورة افتراضية...');
        
        try {
            // محاولة إنشاء صورة افتراضية
            const defaultImageData = bugBountyCore.createDefaultScreenshot(testVulnerability, 'before');
            
            if (defaultImageData) {
                console.log('✅ تم إنشاء صورة افتراضية بنجاح');
                
                // محاولة حفظ الصورة
                const filename = beforeImage.replace('.png', '');
                await bugBountyCore.saveScreenshotToFolder(defaultImageData, filename, folderName);
                console.log(`💾 تم حفظ الصورة: ${filename}.png في مجلد ${folderName}`);
            } else {
                console.log('❌ فشل في إنشاء صورة افتراضية');
            }
        } catch (error) {
            console.log(`⚠️ خطأ في إنشاء الصورة: ${error.message}`);
        }
        
        // اختبار generateReportId المحدث
        console.log('🔄 اختبار generateReportId المحدث...');
        const newReportId = bugBountyCore.generateReportId();
        console.log(`🆔 Report ID الجديد: ${newReportId}`);
        
        // التحقق من النتائج
        console.log('\n📊 ملخص النتائج:');
        console.log(`✅ اسم المجلد: ${folderName}`);
        console.log(`✅ اسم الثغرة: ${cleanVulnName}`);
        console.log(`✅ أسماء الصور:`);
        console.log(`  - ${beforeImage}`);
        console.log(`  - ${duringImage}`);
        console.log(`  - ${afterImage}`);
        console.log(`✅ Report ID: ${newReportId}`);
        
        // التحقق من صحة النمط
        const isCorrectPattern = beforeImage.includes(cleanVulnName) && 
                                beforeImage.includes(folderName) && 
                                beforeImage.startsWith('before_');
        
        console.log(`\n🎯 النمط صحيح: ${isCorrectPattern ? '✅' : '❌'}`);
        
        if (isCorrectPattern) {
            console.log('🎉 جميع الإصلاحات تعمل بشكل صحيح!');
        } else {
            console.log('❌ هناك مشكلة في الإصلاحات');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
    }
}

// تشغيل الاختبار
testSystemWithFixes().then(() => {
    console.log('\n✅ انتهى الاختبار');
}).catch(error => {
    console.error('❌ فشل الاختبار:', error);
});
