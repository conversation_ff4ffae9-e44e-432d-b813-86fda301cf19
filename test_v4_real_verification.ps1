# اختبار حقيقي للنظام v4 مع فحص الملفات والتقارير
Write-Host "🧪 اختبار النظام v4 الحقيقي الشامل" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# متغيرات الاختبار
$targetUrl = "https://testphp.vulnweb.com"
$expectedFolder = "testphp_vulnweb_com"
$screenshotsPath = ".\assets\modules\bugbounty\screenshots"
$reportsPath = "."

Write-Host "🎯 الهدف: $targetUrl" -ForegroundColor Yellow
Write-Host "📁 المجلد المتوقع: $expectedFolder" -ForegroundColor Yellow
Write-Host ""

# فحص 1: التحقق من وجود ملفات النظام
Write-Host "📋 فحص 1: التحقق من ملفات النظام الأساسية" -ForegroundColor Green
Write-Host "-" * 50

$coreFiles = @(
    ".\assets\modules\bugbounty\BugBountyCore.js",
    ".\assets\modules\bugbounty\templates",
    ".\assets\modules\bugbounty\screenshots"
)

foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Write-Host "✅ موجود: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ مفقود: $file" -ForegroundColor Red
    }
}
Write-Host ""

# فحص 2: فحص مجلد الصور
Write-Host "📸 فحص 2: فحص مجلد الصور" -ForegroundColor Green
Write-Host "-" * 50

if (Test-Path $screenshotsPath) {
    Write-Host "✅ مجلد الصور الرئيسي موجود: $screenshotsPath" -ForegroundColor Green
    
    $folders = Get-ChildItem -Path $screenshotsPath -Directory
    Write-Host "📁 المجلدات الموجودة:" -ForegroundColor Cyan
    
    foreach ($folder in $folders) {
        Write-Host "  📂 $($folder.Name)" -ForegroundColor White
        
        if ($folder.Name -eq $expectedFolder) {
            Write-Host "  ✅ تم العثور على المجلد المطلوب!" -ForegroundColor Green
            
            # فحص الصور داخل المجلد
            $targetFolderPath = Join-Path $screenshotsPath $expectedFolder
            $images = Get-ChildItem -Path $targetFolderPath -Filter "*.png" -ErrorAction SilentlyContinue
            
            if ($images) {
                Write-Host "  📸 الصور الموجودة ($($images.Count)):" -ForegroundColor Cyan
                
                $beforeImages = $images | Where-Object { $_.Name -like "before_*" }
                $duringImages = $images | Where-Object { $_.Name -like "during_*" }
                $afterImages = $images | Where-Object { $_.Name -like "after_*" }
                
                Write-Host "    🔒 صور Before: $($beforeImages.Count)" -ForegroundColor Blue
                Write-Host "    ⚠️ صور During: $($duringImages.Count)" -ForegroundColor Yellow
                Write-Host "    🚨 صور After: $($afterImages.Count)" -ForegroundColor Red
                
                # عرض أسماء الصور
                Write-Host "  📋 أسماء الصور:" -ForegroundColor Cyan
                foreach ($image in $images | Select-Object -First 10) {
                    Write-Host "    📸 $($image.Name)" -ForegroundColor White
                }
                
                if ($images.Count -gt 10) {
                    Write-Host "    ... و $($images.Count - 10) صور أخرى" -ForegroundColor Gray
                }
                
                # فحص نمط أسماء الصور
                Write-Host "  🔍 فحص نمط أسماء الصور:" -ForegroundColor Cyan
                $correctPattern = 0
                $totalImages = $images.Count
                
                foreach ($image in $images) {
                    if ($image.Name -match "^(before|during|after)_[A-Za-z0-9_]+_testphp_vulnweb_com\.png$") {
                        $correctPattern++
                    }
                }
                
                $patternPercentage = [math]::Round(($correctPattern / $totalImages) * 100, 2)
                Write-Host "    📊 الصور التي تتبع النمط الصحيح: $correctPattern/$totalImages ($patternPercentage%)" -ForegroundColor $(if ($patternPercentage -ge 80) { "Green" } else { "Yellow" })
                
            } else {
                Write-Host "  ❌ لا توجد صور في المجلد" -ForegroundColor Red
            }
        }
    }
    
    if ($folders.Name -notcontains $expectedFolder) {
        Write-Host "❌ لم يتم العثور على المجلد المطلوب: $expectedFolder" -ForegroundColor Red
        Write-Host "💡 المجلدات الموجودة بدلاً من ذلك:" -ForegroundColor Yellow
        foreach ($folder in $folders) {
            if ($folder.Name -like "*testphp*" -or $folder.Name -like "*vulnweb*") {
                Write-Host "  🔍 $($folder.Name) (مشابه)" -ForegroundColor Yellow
            }
        }
    }
    
} else {
    Write-Host "❌ مجلد الصور الرئيسي غير موجود: $screenshotsPath" -ForegroundColor Red
}
Write-Host ""

# فحص 3: فحص التقارير
Write-Host "📄 فحص 3: فحص التقارير المُنشأة" -ForegroundColor Green
Write-Host "-" * 50

$reportFiles = Get-ChildItem -Path $reportsPath -Filter "*testphp*vulnweb*.html" -ErrorAction SilentlyContinue

if ($reportFiles) {
    Write-Host "✅ تم العثور على $($reportFiles.Count) تقرير:" -ForegroundColor Green
    
    foreach ($report in $reportFiles) {
        Write-Host "  📄 $($report.Name)" -ForegroundColor White
        Write-Host "    📅 تاريخ الإنشاء: $($report.CreationTime)" -ForegroundColor Gray
        Write-Host "    📊 الحجم: $([math]::Round($report.Length / 1KB, 2)) KB" -ForegroundColor Gray
        
        # فحص محتوى التقرير
        try {
            $content = Get-Content -Path $report.FullName -Raw -Encoding UTF8
            
            # فحص وجود قسم التغيرات المرئية
            if ($content -match "التغيرات المرئية والصور الفعلية") {
                Write-Host "    ✅ يحتوي على قسم التغيرات المرئية" -ForegroundColor Green
            } else {
                Write-Host "    ❌ لا يحتوي على قسم التغيرات المرئية" -ForegroundColor Red
            }
            
            # فحص اسم المجلد في التقرير
            if ($content -match $expectedFolder) {
                Write-Host "    ✅ يحتوي على اسم المجلد الصحيح: $expectedFolder" -ForegroundColor Green
            } else {
                Write-Host "    ❌ لا يحتوي على اسم المجلد الصحيح" -ForegroundColor Red
            }
            
            # فحص أسماء الصور في التقرير
            $imageMatches = [regex]::Matches($content, "(before|during|after)_[A-Za-z0-9_]+_testphp_vulnweb_com\.png")
            if ($imageMatches.Count -gt 0) {
                Write-Host "    ✅ يحتوي على $($imageMatches.Count) اسم صورة بالنمط الصحيح" -ForegroundColor Green
            } else {
                Write-Host "    ❌ لا يحتوي على أسماء صور بالنمط الصحيح" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "    ⚠️ خطأ في قراءة محتوى التقرير: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "❌ لم يتم العثور على تقارير" -ForegroundColor Red
    Write-Host "💡 البحث عن أي تقارير HTML..." -ForegroundColor Yellow
    
    $allReports = Get-ChildItem -Path $reportsPath -Filter "*.html" -ErrorAction SilentlyContinue
    if ($allReports) {
        Write-Host "📄 التقارير الموجودة:" -ForegroundColor Cyan
        foreach ($report in $allReports | Select-Object -First 5) {
            Write-Host "  📄 $($report.Name)" -ForegroundColor White
        }
    }
}
Write-Host ""

# فحص 4: اختبار أسماء المجلدات والصور
Write-Host "🔍 فحص 4: اختبار منطق أسماء المجلدات والصور" -ForegroundColor Green
Write-Host "-" * 50

# محاكاة اختبار أسماء المجلدات
$testUrls = @(
    "https://testphp.vulnweb.com",
    "https://example.com",
    "https://test.site.com/path?param=value"
)

Write-Host "📁 اختبار أسماء المجلدات:" -ForegroundColor Cyan
foreach ($url in $testUrls) {
    $cleanUrl = $url -replace "https?://", "" -replace "[/\?&=\.]", "_" -replace "[<>:""|*]", ""
    Write-Host "  🔗 $url → $cleanUrl" -ForegroundColor White
}

# محاكاة اختبار أسماء الصور
Write-Host "📸 اختبار أسماء الصور:" -ForegroundColor Cyan
$testVulnerabilities = @(
    "SQL Injection",
    "Cross-Site Scripting (XSS)",
    "API Authentication Bypass"
)

foreach ($vuln in $testVulnerabilities) {
    $vulnSafeName = $vuln -replace "[^a-zA-Z0-9]", "_"
    $stages = @("before", "during", "after")
    
    Write-Host "  🔍 $vuln → $vulnSafeName" -ForegroundColor White
    foreach ($stage in $stages) {
        $imageName = "${stage}_${vulnSafeName}_testphp_vulnweb_com.png"
        Write-Host "    📸 $imageName" -ForegroundColor Gray
    }
}
Write-Host ""

# فحص 5: النتائج النهائية
Write-Host "🎯 فحص 5: النتائج النهائية" -ForegroundColor Green
Write-Host "-" * 50

$results = @{
    "ملفات النظام" = (Test-Path ".\assets\modules\bugbounty\BugBountyCore.js")
    "مجلد الصور" = (Test-Path $screenshotsPath)
    "المجلد الصحيح" = (Test-Path (Join-Path $screenshotsPath $expectedFolder))
    "وجود تقارير" = ($reportFiles.Count -gt 0)
}

Write-Host "📊 ملخص النتائج:" -ForegroundColor Cyan
foreach ($result in $results.GetEnumerator()) {
    $status = if ($result.Value) { "✅" } else { "❌" }
    $color = if ($result.Value) { "Green" } else { "Red" }
    Write-Host "  $status $($result.Key)" -ForegroundColor $color
}

$passedTests = ($results.Values | Where-Object { $_ }).Count
$totalTests = $results.Count
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host ""
Write-Host "🎉 معدل النجاح: $passedTests/$totalTests ($successRate%)" -ForegroundColor $(if ($successRate -ge 75) { "Green" } elseif ($successRate -ge 50) { "Yellow" } else { "Red" })

if ($successRate -eq 100) {
    Write-Host "🎊 جميع الاختبارات نجحت! النظام v4 يعمل بالإصلاحات الجديدة." -ForegroundColor Green
} elseif ($successRate -ge 75) {
    Write-Host "✅ معظم الاختبارات نجحت. النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة." -ForegroundColor Yellow
} else {
    Write-Host "⚠️ النظام يحتاج إلى مزيد من الإصلاحات." -ForegroundColor Red
}

Write-Host ""
Write-Host "=" * 60 -ForegroundColor Gray
Write-Host "🧪 اكتمل الاختبار الحقيقي للنظام v4" -ForegroundColor Cyan

# إنشاء تقرير نصي
$reportContent = @"
تقرير اختبار النظام v4 الحقيقي
=====================================
التاريخ: $(Get-Date)
الهدف: $targetUrl
المجلد المتوقع: $expectedFolder

النتائج:
$(foreach ($result in $results.GetEnumerator()) {
    $status = if ($result.Value) { "نجح" } else { "فشل" }
    "- $($result.Key): $status"
})

معدل النجاح: $successRate%

الملاحظات:
- تم فحص ملفات النظام الأساسية
- تم فحص مجلد الصور وأسماء الملفات
- تم فحص التقارير المُنشأة
- تم اختبار منطق أسماء المجلدات والصور
"@

$reportContent | Out-File -FilePath "test_v4_verification_report.txt" -Encoding UTF8
Write-Host "📄 تم حفظ تقرير الاختبار في: test_v4_verification_report.txt" -ForegroundColor Cyan
