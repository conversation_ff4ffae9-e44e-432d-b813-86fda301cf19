# Simple V4 System Test
Write-Host "V4 System Test Starting..." -ForegroundColor Cyan
Write-Host "=" * 50

# Test Variables
$targetUrl = "https://testphp.vulnweb.com"
$expectedFolder = "testphp_vulnweb_com"
$screenshotsPath = ".\assets\modules\bugbounty\screenshots"

Write-Host "Target: $targetUrl" -ForegroundColor Yellow
Write-Host "Expected Folder: $expectedFolder" -ForegroundColor Yellow
Write-Host ""

# Test 1: Check Core Files
Write-Host "Test 1: Core System Files" -ForegroundColor Green
Write-Host "-" * 30

$coreFiles = @(
    ".\assets\modules\bugbounty\BugBountyCore.js",
    ".\assets\modules\bugbounty\templates",
    ".\assets\modules\bugbounty\screenshots"
)

$coreFilesOK = $true
foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Write-Host "OK: $file" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $file" -ForegroundColor Red
        $coreFilesOK = $false
    }
}
Write-Host ""

# Test 2: Check Screenshots Folder
Write-Host "Test 2: Screenshots Folder" -ForegroundColor Green
Write-Host "-" * 30

$screenshotsFolderOK = $false
$correctFolderFound = $false
$imageCount = 0
$correctImageNames = 0

if (Test-Path $screenshotsPath) {
    Write-Host "OK: Screenshots main folder exists" -ForegroundColor Green
    $screenshotsFolderOK = $true
    
    $folders = Get-ChildItem -Path $screenshotsPath -Directory
    Write-Host "Found folders: $($folders.Count)" -ForegroundColor Cyan
    
    foreach ($folder in $folders) {
        Write-Host "  Folder: $($folder.Name)" -ForegroundColor White
        
        if ($folder.Name -eq $expectedFolder) {
            Write-Host "  FOUND CORRECT FOLDER!" -ForegroundColor Green
            $correctFolderFound = $true
            
            # Check images in folder
            $targetFolderPath = Join-Path $screenshotsPath $expectedFolder
            $images = Get-ChildItem -Path $targetFolderPath -Filter "*.png" -ErrorAction SilentlyContinue
            
            if ($images) {
                $imageCount = $images.Count
                Write-Host "  Images found: $imageCount" -ForegroundColor Cyan
                
                # Check image naming pattern
                foreach ($image in $images) {
                    if ($image.Name -match "^(before|during|after)_[A-Za-z0-9_]+_testphp_vulnweb_com\.png$") {
                        $correctImageNames++
                    }
                }
                
                Write-Host "  Correct naming pattern: $correctImageNames/$imageCount" -ForegroundColor $(if ($correctImageNames -eq $imageCount) { "Green" } else { "Yellow" })
                
                # Show first few image names
                Write-Host "  Sample image names:" -ForegroundColor Cyan
                foreach ($image in $images | Select-Object -First 5) {
                    Write-Host "    $($image.Name)" -ForegroundColor White
                }
                
            } else {
                Write-Host "  NO IMAGES FOUND" -ForegroundColor Red
            }
        }
    }
    
    if (-not $correctFolderFound) {
        Write-Host "MISSING: Expected folder not found: $expectedFolder" -ForegroundColor Red
    }
    
} else {
    Write-Host "MISSING: Screenshots main folder" -ForegroundColor Red
}
Write-Host ""

# Test 3: Check Reports
Write-Host "Test 3: Generated Reports" -ForegroundColor Green
Write-Host "-" * 30

$reportFiles = Get-ChildItem -Path "." -Filter "*testphp*vulnweb*.html" -ErrorAction SilentlyContinue
$reportsOK = $false

if ($reportFiles) {
    Write-Host "Found reports: $($reportFiles.Count)" -ForegroundColor Green
    $reportsOK = $true
    
    foreach ($report in $reportFiles) {
        Write-Host "  Report: $($report.Name)" -ForegroundColor White
        Write-Host "    Size: $([math]::Round($report.Length / 1KB, 2)) KB" -ForegroundColor Gray
        Write-Host "    Created: $($report.CreationTime)" -ForegroundColor Gray
    }
} else {
    Write-Host "NO REPORTS FOUND" -ForegroundColor Red
    
    # Check for any HTML reports
    $allReports = Get-ChildItem -Path "." -Filter "*.html" -ErrorAction SilentlyContinue
    if ($allReports) {
        Write-Host "Other HTML files found:" -ForegroundColor Yellow
        foreach ($report in $allReports | Select-Object -First 3) {
            Write-Host "  $($report.Name)" -ForegroundColor White
        }
    }
}
Write-Host ""

# Test 4: Naming Logic Test
Write-Host "Test 4: Naming Logic Test" -ForegroundColor Green
Write-Host "-" * 30

# Test folder naming
$testUrls = @(
    "https://testphp.vulnweb.com",
    "https://example.com"
)

Write-Host "Folder naming test:" -ForegroundColor Cyan
foreach ($url in $testUrls) {
    $cleanUrl = $url -replace "https?://", "" -replace "[/\?&=\.]", "_" -replace "[<>:""|*]", ""
    Write-Host "  $url -> $cleanUrl" -ForegroundColor White
}

# Test image naming
Write-Host "Image naming test:" -ForegroundColor Cyan
$testVulns = @("SQL Injection", "XSS", "API Authentication Bypass")

foreach ($vuln in $testVulns) {
    $vulnSafeName = $vuln -replace "[^a-zA-Z0-9]", "_"
    Write-Host "  $vuln -> $vulnSafeName" -ForegroundColor White
    
    $stages = @("before", "during", "after")
    foreach ($stage in $stages) {
        $imageName = "${stage}_${vulnSafeName}_testphp_vulnweb_com.png"
        Write-Host "    $imageName" -ForegroundColor Gray
    }
}
Write-Host ""

# Final Results
Write-Host "Final Results" -ForegroundColor Green
Write-Host "-" * 30

$results = @{
    "Core Files" = $coreFilesOK
    "Screenshots Folder" = $screenshotsFolderOK
    "Correct Folder" = $correctFolderFound
    "Images Found" = ($imageCount -gt 0)
    "Correct Image Names" = ($correctImageNames -gt 0)
    "Reports Generated" = $reportsOK
}

Write-Host "Test Summary:" -ForegroundColor Cyan
foreach ($result in $results.GetEnumerator()) {
    $status = if ($result.Value) { "PASS" } else { "FAIL" }
    $color = if ($result.Value) { "Green" } else { "Red" }
    Write-Host "  $status - $($result.Key)" -ForegroundColor $color
}

$passedTests = ($results.Values | Where-Object { $_ }).Count
$totalTests = $results.Count
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host ""
Write-Host "Success Rate: $passedTests/$totalTests ($successRate%)" -ForegroundColor $(if ($successRate -ge 75) { "Green" } elseif ($successRate -ge 50) { "Yellow" } else { "Red" })

if ($successRate -eq 100) {
    Write-Host "ALL TESTS PASSED! V4 system working with fixes." -ForegroundColor Green
} elseif ($successRate -ge 75) {
    Write-Host "MOST TESTS PASSED. System working well." -ForegroundColor Yellow
} else {
    Write-Host "SYSTEM NEEDS MORE FIXES." -ForegroundColor Red
}

Write-Host ""
Write-Host "Detailed Info:" -ForegroundColor Cyan
Write-Host "  Expected folder: $expectedFolder"
Write-Host "  Images found: $imageCount"
Write-Host "  Correct image names: $correctImageNames"
Write-Host "  Reports found: $($reportFiles.Count)"

Write-Host ""
Write-Host "=" * 50
Write-Host "V4 System Test Complete" -ForegroundColor Cyan
