// اختبار إصلاحات نظام الصور في Bug Bounty v4.0
console.log('🧪 بدء اختبار إصلاحات نظام الصور...');

// محاكاة بيانات الثغرة للاختبار
const testVulnerability = {
    name: 'SQL Injection',
    url: 'https://testphp.vulnweb.com',
    target_url: 'https://testphp.vulnweb.com',
    location: 'https://testphp.vulnweb.com',
    visual_proof: {
        before_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        during_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        after_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
};

// محاكاة BugBountyCore
const mockBugBountyCore = {
    analysisState: {
        reportId: 'report_1753009791337_cxw3nzhcr',
        currentScanFolder: 'testphp_vulnweb_com'
    },
    
    // دالة للحصول على اسم المجلد الصحيح
    getCorrectFolderName(vulnerability) {
        const targetUrl = vulnerability.url || vulnerability.target_url || vulnerability.location || window.location.href;
        const cleanUrl = targetUrl.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');

        // الأولوية لاسم الرابط
        return cleanUrl;
    },
    
    // دالة البحث عن الصور المحدثة
    findRealImageForVulnerability(vuln, stage) {
        console.log(`🔍 البحث عن صورة ${stage} للثغرة: ${vuln.name}`);

        const targetUrl = vuln.url || vuln.target_url || vuln.location || window.location.href;
        const cleanUrl = targetUrl.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');

        // الأولوية لاسم الرابط
        let folderName = cleanUrl;
        
        const vulnName = vuln.name || vuln.type || '';
        const cleanVulnName = vulnName.replace(/\s+/g, '_').replace(/[<>:"|*]/g, '');
        const imageName = `${stage}_${cleanVulnName}_${cleanUrl}.png`;
        const imagePath = `./assets/modules/bugbounty/screenshots/${folderName}/${imageName}`;

        console.log(`🔍 البحث عن الصورة: ${imageName} في المجلد: ${folderName}`);
        console.log(`🔍 المسار الكامل: ${imagePath}`);

        if (!this.analysisState.currentScanFolder) {
            this.analysisState.currentScanFolder = folderName;
        }

        if (vuln.visual_proof && vuln.visual_proof[`${stage}_screenshot`]) {
            console.log(`✅ تم العثور على صورة ${stage} في بيانات الثغرة`);
            return vuln.visual_proof[`${stage}_screenshot`];
        }

        if (vuln.screenshots && vuln.screenshots[stage]) {
            console.log(`✅ تم العثور على صورة ${stage} في screenshots`);
            return vuln.screenshots[stage];
        }

        console.log(`❌ لم يتم العثور على صورة ${stage} للثغرة: ${vuln.name}`);
        return null;
    }
};

// اختبار الدوال
console.log('\n🔍 اختبار دالة getCorrectFolderName...');
const folderName = mockBugBountyCore.getCorrectFolderName(testVulnerability);
console.log(`📁 اسم المجلد: ${folderName}`);

console.log('\n🔍 اختبار دالة findRealImageForVulnerability...');
const beforeImage = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'before');
const duringImage = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'during');
const afterImage = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'after');

console.log(`📸 صورة before: ${beforeImage ? 'موجودة' : 'غير موجودة'}`);
console.log(`📸 صورة during: ${duringImage ? 'موجودة' : 'غير موجودة'}`);
console.log(`📸 صورة after: ${afterImage ? 'موجودة' : 'غير موجودة'}`);

// اختبار أسماء الصور المتوقعة
console.log('\n📋 أسماء الصور المتوقعة:');
const expectedFolderName = 'testphp_vulnweb_com';
const expectedImageNames = [
    `before_SQL_Injection_${expectedFolderName}.png`,
    `during_SQL_Injection_${expectedFolderName}.png`,
    `after_SQL_Injection_${expectedFolderName}.png`
];

expectedImageNames.forEach(imageName => {
    console.log(`📸 ${imageName}`);
});

console.log('\n📁 المسار المتوقع للمجلد:');
console.log(`assets/modules/bugbounty/screenshots/${expectedFolderName}/`);

console.log('\n✅ اختبار إصلاحات نظام الصور مكتمل!');
